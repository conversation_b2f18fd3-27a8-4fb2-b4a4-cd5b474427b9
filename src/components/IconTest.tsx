import React from 'react';
import {View, Text, StyleSheet, ScrollView} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

const IconTest: React.FC = () => {
  const testIcons = [
    'home',
    'restaurant-menu',
    'person',
    'shopping-cart',
    'notifications',
    'receipt',
    'local-offer',
    'menu',
    'search',
    'favorite',
    'star',
    'settings',
    'info',
    'help',
    'phone',
    'email',
    'location-on',
    'add',
    'remove',
    'edit',
    'check',
    'close',
    'arrow-back',
    'image',
  ];

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Icon Test Screen</Text>
      <Text style={styles.subtitle}>
        If you can see icons below, react-native-vector-icons is working
        correctly:
      </Text>

      <View style={styles.iconGrid}>
        {testIcons.map((iconName, index) => (
          <View key={index} style={styles.iconItem}>
            <View style={styles.iconContainer}>
              <Icon name={iconName} size={30} color="#000000" />
              <Text style={styles.fallbackText}>📱</Text>
            </View>
            <Text style={styles.iconLabel}>{iconName}</Text>
          </View>
        ))}
      </View>

      <Text style={styles.note}>
        Note: If you see empty squares or question marks instead of icons, the
        font files are not properly linked.
      </Text>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
    color: '#000000',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
    color: '#666666',
  },
  iconGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  iconItem: {
    alignItems: 'center',
    margin: 10,
    width: 80,
  },
  iconContainer: {
    position: 'relative',
    width: 30,
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fallbackText: {
    position: 'absolute',
    fontSize: 20,
    opacity: 0.3,
  },
  iconLabel: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 5,
    color: '#333333',
  },
  note: {
    fontSize: 14,
    fontStyle: 'italic',
    textAlign: 'center',
    color: '#888888',
    marginTop: 20,
  },
});

export default IconTest;
