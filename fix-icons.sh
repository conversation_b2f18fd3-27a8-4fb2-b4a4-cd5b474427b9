#!/bin/bash

echo "🔧 Fixing React Native Vector Icons..."

# Clean everything
echo "🧹 Cleaning project..."
rm -rf node_modules
rm -rf ios/build
rm -rf android/app/build
rm -rf android/.gradle

# Reinstall dependencies
echo "📦 Reinstalling dependencies..."
npm install

# Link assets (for RN < 0.60 compatibility)
echo "🔗 Linking assets..."
npx react-native-asset

# For iOS - clean and rebuild
echo "🍎 Cleaning iOS..."
cd ios
rm -rf build
rm -rf Pods
rm -rf Podfile.lock
pod install
cd ..

# Clean Metro cache
echo "🚇 Cleaning Metro cache..."
npx react-native start --reset-cache &
METRO_PID=$!

# Wait a moment for Metro to start
sleep 5

# Kill Metro
kill $METRO_PID

echo "✅ Setup complete!"
echo ""
echo "📱 Now run one of these commands:"
echo "   For iOS: npx react-native run-ios"
echo "   For Android: npx react-native run-android"
echo ""
echo "🧪 To test icons, navigate to the IconTest screen in your app"
