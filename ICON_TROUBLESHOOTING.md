# 🔧 Material Icons Troubleshooting Guide

## 🚨 Problem: Icons Not Displaying

If you're seeing empty squares, question marks, or no icons at all, follow this step-by-step guide.

## ✅ Quick Fix (Recommended)

Run the automated fix script:

```bash
./fix-icons.sh
```

Then rebuild your app:

```bash
# For iOS
npx react-native run-ios

# For Android  
npx react-native run-android
```

## 🔍 Manual Troubleshooting Steps

### Step 1: Verify Configuration Files

✅ **Check android/app/build.gradle** - Should have this line at the bottom:
```gradle
apply from: file("../../node_modules/react-native-vector-icons/fonts.gradle")
```

✅ **Check ios/KhanBaba/Info.plist** - Should include MaterialIcons.ttf:
```xml
<key>UIAppFonts</key>
<array>
    <string>MaterialIcons.ttf</string>
    <!-- other fonts -->
</array>
```

✅ **Check react-native.config.js** - Should be properly configured for vector icons

### Step 2: Clean and Rebuild

```bash
# Clean everything
rm -rf node_modules
rm -rf ios/build  
rm -rf android/app/build
rm -rf android/.gradle

# Reinstall
npm install

# iOS specific
cd ios
rm -rf Pods Podfile.lock
pod install
cd ..

# Link assets
npx react-native-asset

# Clean Metro cache
npx react-native start --reset-cache
```

### Step 3: Platform-Specific Fixes

#### For iOS:
1. Open `ios/KhanBaba.xcworkspace` in Xcode
2. Clean Build Folder (Cmd+Shift+K)
3. Build and run

#### For Android:
1. Make sure fonts.gradle is applied in build.gradle
2. Clean project: `cd android && ./gradlew clean && cd ..`
3. Rebuild: `npx react-native run-android`

### Step 4: Test Icons

Navigate to the IconTest screen in your app to verify icons are working.

## 🐛 Common Issues

### Issue 1: "Unrecognized font family MaterialIcons"
**Solution:** Font files not properly linked. Run the clean/rebuild process.

### Issue 2: Icons show as squares/boxes
**Solution:** Font files missing or corrupted. Re-run `npx react-native-asset`.

### Issue 3: Works on one platform but not the other
**Solution:** Platform-specific configuration issue. Check Info.plist (iOS) or fonts.gradle (Android).

### Issue 4: Metro bundler cache issues
**Solution:** Clear Metro cache: `npx react-native start --reset-cache`

## 🧪 Testing

Use the IconTest component to verify icons are working:

```typescript
import IconTest from './src/components/IconTest';

// Navigate to IconTest screen to see all icons
```

## 📱 Expected Result

After fixing, you should see:
- ✅ Material Icons displaying correctly throughout the app
- ✅ Tab navigation icons visible
- ✅ Drawer menu icons visible  
- ✅ All screen icons working properly

## 🆘 Still Having Issues?

1. Check React Native version compatibility
2. Verify react-native-vector-icons version (should be ^10.2.0)
3. Try creating a fresh React Native project to test
4. Check device/simulator logs for font-related errors

## 📋 Verification Checklist

- [ ] Icons visible in tab navigation
- [ ] Icons visible in drawer menu
- [ ] Icons visible in all screens
- [ ] No empty squares or question marks
- [ ] Both iOS and Android working
- [ ] IconTest screen shows all icons correctly
