# /* Copyright (C) 2001
#  * Housemarque Oy
#  * http://www.housemarque.com
#  *
#  * Distributed under the Boost Software License, Version 1.0. (See
#  * accompanying file LICENSE_1_0.txt or copy at
#  * http://www.boost.org/LICENSE_1_0.txt)
#  */
#
# /* Revised by <PERSON> (2002) */
#
# /* See http://www.boost.org for most recent version. */
#
# ifndef BOOST_PREPROCESSOR_CONTROL_DETAIL_EDG_WHILE_256_HPP
# define BOOST_PREPROCESSOR_CONTROL_DETAIL_EDG_WHILE_256_HPP
#
# define BOOST_PP_WHILE_0(p, o, s) BOOST_PP_WHILE_0_I(p, o, s)
# define BOOST_PP_WHILE_1(p, o, s) BOOST_PP_WHILE_1_I(p, o, s)
# define BOOST_PP_WHILE_2(p, o, s) BOOST_PP_WHILE_2_I(p, o, s)
# define BOOST_PP_WHILE_3(p, o, s) BOOST_PP_WHILE_3_I(p, o, s)
# define BOOST_PP_WHILE_4(p, o, s) BOOST_PP_WHILE_4_I(p, o, s)
# define BOOST_PP_WHILE_5(p, o, s) BOOST_PP_WHILE_5_I(p, o, s)
# define BOOST_PP_WHILE_6(p, o, s) BOOST_PP_WHILE_6_I(p, o, s)
# define BOOST_PP_WHILE_7(p, o, s) BOOST_PP_WHILE_7_I(p, o, s)
# define BOOST_PP_WHILE_8(p, o, s) BOOST_PP_WHILE_8_I(p, o, s)
# define BOOST_PP_WHILE_9(p, o, s) BOOST_PP_WHILE_9_I(p, o, s)
# define BOOST_PP_WHILE_10(p, o, s) BOOST_PP_WHILE_10_I(p, o, s)
# define BOOST_PP_WHILE_11(p, o, s) BOOST_PP_WHILE_11_I(p, o, s)
# define BOOST_PP_WHILE_12(p, o, s) BOOST_PP_WHILE_12_I(p, o, s)
# define BOOST_PP_WHILE_13(p, o, s) BOOST_PP_WHILE_13_I(p, o, s)
# define BOOST_PP_WHILE_14(p, o, s) BOOST_PP_WHILE_14_I(p, o, s)
# define BOOST_PP_WHILE_15(p, o, s) BOOST_PP_WHILE_15_I(p, o, s)
# define BOOST_PP_WHILE_16(p, o, s) BOOST_PP_WHILE_16_I(p, o, s)
# define BOOST_PP_WHILE_17(p, o, s) BOOST_PP_WHILE_17_I(p, o, s)
# define BOOST_PP_WHILE_18(p, o, s) BOOST_PP_WHILE_18_I(p, o, s)
# define BOOST_PP_WHILE_19(p, o, s) BOOST_PP_WHILE_19_I(p, o, s)
# define BOOST_PP_WHILE_20(p, o, s) BOOST_PP_WHILE_20_I(p, o, s)
# define BOOST_PP_WHILE_21(p, o, s) BOOST_PP_WHILE_21_I(p, o, s)
# define BOOST_PP_WHILE_22(p, o, s) BOOST_PP_WHILE_22_I(p, o, s)
# define BOOST_PP_WHILE_23(p, o, s) BOOST_PP_WHILE_23_I(p, o, s)
# define BOOST_PP_WHILE_24(p, o, s) BOOST_PP_WHILE_24_I(p, o, s)
# define BOOST_PP_WHILE_25(p, o, s) BOOST_PP_WHILE_25_I(p, o, s)
# define BOOST_PP_WHILE_26(p, o, s) BOOST_PP_WHILE_26_I(p, o, s)
# define BOOST_PP_WHILE_27(p, o, s) BOOST_PP_WHILE_27_I(p, o, s)
# define BOOST_PP_WHILE_28(p, o, s) BOOST_PP_WHILE_28_I(p, o, s)
# define BOOST_PP_WHILE_29(p, o, s) BOOST_PP_WHILE_29_I(p, o, s)
# define BOOST_PP_WHILE_30(p, o, s) BOOST_PP_WHILE_30_I(p, o, s)
# define BOOST_PP_WHILE_31(p, o, s) BOOST_PP_WHILE_31_I(p, o, s)
# define BOOST_PP_WHILE_32(p, o, s) BOOST_PP_WHILE_32_I(p, o, s)
# define BOOST_PP_WHILE_33(p, o, s) BOOST_PP_WHILE_33_I(p, o, s)
# define BOOST_PP_WHILE_34(p, o, s) BOOST_PP_WHILE_34_I(p, o, s)
# define BOOST_PP_WHILE_35(p, o, s) BOOST_PP_WHILE_35_I(p, o, s)
# define BOOST_PP_WHILE_36(p, o, s) BOOST_PP_WHILE_36_I(p, o, s)
# define BOOST_PP_WHILE_37(p, o, s) BOOST_PP_WHILE_37_I(p, o, s)
# define BOOST_PP_WHILE_38(p, o, s) BOOST_PP_WHILE_38_I(p, o, s)
# define BOOST_PP_WHILE_39(p, o, s) BOOST_PP_WHILE_39_I(p, o, s)
# define BOOST_PP_WHILE_40(p, o, s) BOOST_PP_WHILE_40_I(p, o, s)
# define BOOST_PP_WHILE_41(p, o, s) BOOST_PP_WHILE_41_I(p, o, s)
# define BOOST_PP_WHILE_42(p, o, s) BOOST_PP_WHILE_42_I(p, o, s)
# define BOOST_PP_WHILE_43(p, o, s) BOOST_PP_WHILE_43_I(p, o, s)
# define BOOST_PP_WHILE_44(p, o, s) BOOST_PP_WHILE_44_I(p, o, s)
# define BOOST_PP_WHILE_45(p, o, s) BOOST_PP_WHILE_45_I(p, o, s)
# define BOOST_PP_WHILE_46(p, o, s) BOOST_PP_WHILE_46_I(p, o, s)
# define BOOST_PP_WHILE_47(p, o, s) BOOST_PP_WHILE_47_I(p, o, s)
# define BOOST_PP_WHILE_48(p, o, s) BOOST_PP_WHILE_48_I(p, o, s)
# define BOOST_PP_WHILE_49(p, o, s) BOOST_PP_WHILE_49_I(p, o, s)
# define BOOST_PP_WHILE_50(p, o, s) BOOST_PP_WHILE_50_I(p, o, s)
# define BOOST_PP_WHILE_51(p, o, s) BOOST_PP_WHILE_51_I(p, o, s)
# define BOOST_PP_WHILE_52(p, o, s) BOOST_PP_WHILE_52_I(p, o, s)
# define BOOST_PP_WHILE_53(p, o, s) BOOST_PP_WHILE_53_I(p, o, s)
# define BOOST_PP_WHILE_54(p, o, s) BOOST_PP_WHILE_54_I(p, o, s)
# define BOOST_PP_WHILE_55(p, o, s) BOOST_PP_WHILE_55_I(p, o, s)
# define BOOST_PP_WHILE_56(p, o, s) BOOST_PP_WHILE_56_I(p, o, s)
# define BOOST_PP_WHILE_57(p, o, s) BOOST_PP_WHILE_57_I(p, o, s)
# define BOOST_PP_WHILE_58(p, o, s) BOOST_PP_WHILE_58_I(p, o, s)
# define BOOST_PP_WHILE_59(p, o, s) BOOST_PP_WHILE_59_I(p, o, s)
# define BOOST_PP_WHILE_60(p, o, s) BOOST_PP_WHILE_60_I(p, o, s)
# define BOOST_PP_WHILE_61(p, o, s) BOOST_PP_WHILE_61_I(p, o, s)
# define BOOST_PP_WHILE_62(p, o, s) BOOST_PP_WHILE_62_I(p, o, s)
# define BOOST_PP_WHILE_63(p, o, s) BOOST_PP_WHILE_63_I(p, o, s)
# define BOOST_PP_WHILE_64(p, o, s) BOOST_PP_WHILE_64_I(p, o, s)
# define BOOST_PP_WHILE_65(p, o, s) BOOST_PP_WHILE_65_I(p, o, s)
# define BOOST_PP_WHILE_66(p, o, s) BOOST_PP_WHILE_66_I(p, o, s)
# define BOOST_PP_WHILE_67(p, o, s) BOOST_PP_WHILE_67_I(p, o, s)
# define BOOST_PP_WHILE_68(p, o, s) BOOST_PP_WHILE_68_I(p, o, s)
# define BOOST_PP_WHILE_69(p, o, s) BOOST_PP_WHILE_69_I(p, o, s)
# define BOOST_PP_WHILE_70(p, o, s) BOOST_PP_WHILE_70_I(p, o, s)
# define BOOST_PP_WHILE_71(p, o, s) BOOST_PP_WHILE_71_I(p, o, s)
# define BOOST_PP_WHILE_72(p, o, s) BOOST_PP_WHILE_72_I(p, o, s)
# define BOOST_PP_WHILE_73(p, o, s) BOOST_PP_WHILE_73_I(p, o, s)
# define BOOST_PP_WHILE_74(p, o, s) BOOST_PP_WHILE_74_I(p, o, s)
# define BOOST_PP_WHILE_75(p, o, s) BOOST_PP_WHILE_75_I(p, o, s)
# define BOOST_PP_WHILE_76(p, o, s) BOOST_PP_WHILE_76_I(p, o, s)
# define BOOST_PP_WHILE_77(p, o, s) BOOST_PP_WHILE_77_I(p, o, s)
# define BOOST_PP_WHILE_78(p, o, s) BOOST_PP_WHILE_78_I(p, o, s)
# define BOOST_PP_WHILE_79(p, o, s) BOOST_PP_WHILE_79_I(p, o, s)
# define BOOST_PP_WHILE_80(p, o, s) BOOST_PP_WHILE_80_I(p, o, s)
# define BOOST_PP_WHILE_81(p, o, s) BOOST_PP_WHILE_81_I(p, o, s)
# define BOOST_PP_WHILE_82(p, o, s) BOOST_PP_WHILE_82_I(p, o, s)
# define BOOST_PP_WHILE_83(p, o, s) BOOST_PP_WHILE_83_I(p, o, s)
# define BOOST_PP_WHILE_84(p, o, s) BOOST_PP_WHILE_84_I(p, o, s)
# define BOOST_PP_WHILE_85(p, o, s) BOOST_PP_WHILE_85_I(p, o, s)
# define BOOST_PP_WHILE_86(p, o, s) BOOST_PP_WHILE_86_I(p, o, s)
# define BOOST_PP_WHILE_87(p, o, s) BOOST_PP_WHILE_87_I(p, o, s)
# define BOOST_PP_WHILE_88(p, o, s) BOOST_PP_WHILE_88_I(p, o, s)
# define BOOST_PP_WHILE_89(p, o, s) BOOST_PP_WHILE_89_I(p, o, s)
# define BOOST_PP_WHILE_90(p, o, s) BOOST_PP_WHILE_90_I(p, o, s)
# define BOOST_PP_WHILE_91(p, o, s) BOOST_PP_WHILE_91_I(p, o, s)
# define BOOST_PP_WHILE_92(p, o, s) BOOST_PP_WHILE_92_I(p, o, s)
# define BOOST_PP_WHILE_93(p, o, s) BOOST_PP_WHILE_93_I(p, o, s)
# define BOOST_PP_WHILE_94(p, o, s) BOOST_PP_WHILE_94_I(p, o, s)
# define BOOST_PP_WHILE_95(p, o, s) BOOST_PP_WHILE_95_I(p, o, s)
# define BOOST_PP_WHILE_96(p, o, s) BOOST_PP_WHILE_96_I(p, o, s)
# define BOOST_PP_WHILE_97(p, o, s) BOOST_PP_WHILE_97_I(p, o, s)
# define BOOST_PP_WHILE_98(p, o, s) BOOST_PP_WHILE_98_I(p, o, s)
# define BOOST_PP_WHILE_99(p, o, s) BOOST_PP_WHILE_99_I(p, o, s)
# define BOOST_PP_WHILE_100(p, o, s) BOOST_PP_WHILE_100_I(p, o, s)
# define BOOST_PP_WHILE_101(p, o, s) BOOST_PP_WHILE_101_I(p, o, s)
# define BOOST_PP_WHILE_102(p, o, s) BOOST_PP_WHILE_102_I(p, o, s)
# define BOOST_PP_WHILE_103(p, o, s) BOOST_PP_WHILE_103_I(p, o, s)
# define BOOST_PP_WHILE_104(p, o, s) BOOST_PP_WHILE_104_I(p, o, s)
# define BOOST_PP_WHILE_105(p, o, s) BOOST_PP_WHILE_105_I(p, o, s)
# define BOOST_PP_WHILE_106(p, o, s) BOOST_PP_WHILE_106_I(p, o, s)
# define BOOST_PP_WHILE_107(p, o, s) BOOST_PP_WHILE_107_I(p, o, s)
# define BOOST_PP_WHILE_108(p, o, s) BOOST_PP_WHILE_108_I(p, o, s)
# define BOOST_PP_WHILE_109(p, o, s) BOOST_PP_WHILE_109_I(p, o, s)
# define BOOST_PP_WHILE_110(p, o, s) BOOST_PP_WHILE_110_I(p, o, s)
# define BOOST_PP_WHILE_111(p, o, s) BOOST_PP_WHILE_111_I(p, o, s)
# define BOOST_PP_WHILE_112(p, o, s) BOOST_PP_WHILE_112_I(p, o, s)
# define BOOST_PP_WHILE_113(p, o, s) BOOST_PP_WHILE_113_I(p, o, s)
# define BOOST_PP_WHILE_114(p, o, s) BOOST_PP_WHILE_114_I(p, o, s)
# define BOOST_PP_WHILE_115(p, o, s) BOOST_PP_WHILE_115_I(p, o, s)
# define BOOST_PP_WHILE_116(p, o, s) BOOST_PP_WHILE_116_I(p, o, s)
# define BOOST_PP_WHILE_117(p, o, s) BOOST_PP_WHILE_117_I(p, o, s)
# define BOOST_PP_WHILE_118(p, o, s) BOOST_PP_WHILE_118_I(p, o, s)
# define BOOST_PP_WHILE_119(p, o, s) BOOST_PP_WHILE_119_I(p, o, s)
# define BOOST_PP_WHILE_120(p, o, s) BOOST_PP_WHILE_120_I(p, o, s)
# define BOOST_PP_WHILE_121(p, o, s) BOOST_PP_WHILE_121_I(p, o, s)
# define BOOST_PP_WHILE_122(p, o, s) BOOST_PP_WHILE_122_I(p, o, s)
# define BOOST_PP_WHILE_123(p, o, s) BOOST_PP_WHILE_123_I(p, o, s)
# define BOOST_PP_WHILE_124(p, o, s) BOOST_PP_WHILE_124_I(p, o, s)
# define BOOST_PP_WHILE_125(p, o, s) BOOST_PP_WHILE_125_I(p, o, s)
# define BOOST_PP_WHILE_126(p, o, s) BOOST_PP_WHILE_126_I(p, o, s)
# define BOOST_PP_WHILE_127(p, o, s) BOOST_PP_WHILE_127_I(p, o, s)
# define BOOST_PP_WHILE_128(p, o, s) BOOST_PP_WHILE_128_I(p, o, s)
# define BOOST_PP_WHILE_129(p, o, s) BOOST_PP_WHILE_129_I(p, o, s)
# define BOOST_PP_WHILE_130(p, o, s) BOOST_PP_WHILE_130_I(p, o, s)
# define BOOST_PP_WHILE_131(p, o, s) BOOST_PP_WHILE_131_I(p, o, s)
# define BOOST_PP_WHILE_132(p, o, s) BOOST_PP_WHILE_132_I(p, o, s)
# define BOOST_PP_WHILE_133(p, o, s) BOOST_PP_WHILE_133_I(p, o, s)
# define BOOST_PP_WHILE_134(p, o, s) BOOST_PP_WHILE_134_I(p, o, s)
# define BOOST_PP_WHILE_135(p, o, s) BOOST_PP_WHILE_135_I(p, o, s)
# define BOOST_PP_WHILE_136(p, o, s) BOOST_PP_WHILE_136_I(p, o, s)
# define BOOST_PP_WHILE_137(p, o, s) BOOST_PP_WHILE_137_I(p, o, s)
# define BOOST_PP_WHILE_138(p, o, s) BOOST_PP_WHILE_138_I(p, o, s)
# define BOOST_PP_WHILE_139(p, o, s) BOOST_PP_WHILE_139_I(p, o, s)
# define BOOST_PP_WHILE_140(p, o, s) BOOST_PP_WHILE_140_I(p, o, s)
# define BOOST_PP_WHILE_141(p, o, s) BOOST_PP_WHILE_141_I(p, o, s)
# define BOOST_PP_WHILE_142(p, o, s) BOOST_PP_WHILE_142_I(p, o, s)
# define BOOST_PP_WHILE_143(p, o, s) BOOST_PP_WHILE_143_I(p, o, s)
# define BOOST_PP_WHILE_144(p, o, s) BOOST_PP_WHILE_144_I(p, o, s)
# define BOOST_PP_WHILE_145(p, o, s) BOOST_PP_WHILE_145_I(p, o, s)
# define BOOST_PP_WHILE_146(p, o, s) BOOST_PP_WHILE_146_I(p, o, s)
# define BOOST_PP_WHILE_147(p, o, s) BOOST_PP_WHILE_147_I(p, o, s)
# define BOOST_PP_WHILE_148(p, o, s) BOOST_PP_WHILE_148_I(p, o, s)
# define BOOST_PP_WHILE_149(p, o, s) BOOST_PP_WHILE_149_I(p, o, s)
# define BOOST_PP_WHILE_150(p, o, s) BOOST_PP_WHILE_150_I(p, o, s)
# define BOOST_PP_WHILE_151(p, o, s) BOOST_PP_WHILE_151_I(p, o, s)
# define BOOST_PP_WHILE_152(p, o, s) BOOST_PP_WHILE_152_I(p, o, s)
# define BOOST_PP_WHILE_153(p, o, s) BOOST_PP_WHILE_153_I(p, o, s)
# define BOOST_PP_WHILE_154(p, o, s) BOOST_PP_WHILE_154_I(p, o, s)
# define BOOST_PP_WHILE_155(p, o, s) BOOST_PP_WHILE_155_I(p, o, s)
# define BOOST_PP_WHILE_156(p, o, s) BOOST_PP_WHILE_156_I(p, o, s)
# define BOOST_PP_WHILE_157(p, o, s) BOOST_PP_WHILE_157_I(p, o, s)
# define BOOST_PP_WHILE_158(p, o, s) BOOST_PP_WHILE_158_I(p, o, s)
# define BOOST_PP_WHILE_159(p, o, s) BOOST_PP_WHILE_159_I(p, o, s)
# define BOOST_PP_WHILE_160(p, o, s) BOOST_PP_WHILE_160_I(p, o, s)
# define BOOST_PP_WHILE_161(p, o, s) BOOST_PP_WHILE_161_I(p, o, s)
# define BOOST_PP_WHILE_162(p, o, s) BOOST_PP_WHILE_162_I(p, o, s)
# define BOOST_PP_WHILE_163(p, o, s) BOOST_PP_WHILE_163_I(p, o, s)
# define BOOST_PP_WHILE_164(p, o, s) BOOST_PP_WHILE_164_I(p, o, s)
# define BOOST_PP_WHILE_165(p, o, s) BOOST_PP_WHILE_165_I(p, o, s)
# define BOOST_PP_WHILE_166(p, o, s) BOOST_PP_WHILE_166_I(p, o, s)
# define BOOST_PP_WHILE_167(p, o, s) BOOST_PP_WHILE_167_I(p, o, s)
# define BOOST_PP_WHILE_168(p, o, s) BOOST_PP_WHILE_168_I(p, o, s)
# define BOOST_PP_WHILE_169(p, o, s) BOOST_PP_WHILE_169_I(p, o, s)
# define BOOST_PP_WHILE_170(p, o, s) BOOST_PP_WHILE_170_I(p, o, s)
# define BOOST_PP_WHILE_171(p, o, s) BOOST_PP_WHILE_171_I(p, o, s)
# define BOOST_PP_WHILE_172(p, o, s) BOOST_PP_WHILE_172_I(p, o, s)
# define BOOST_PP_WHILE_173(p, o, s) BOOST_PP_WHILE_173_I(p, o, s)
# define BOOST_PP_WHILE_174(p, o, s) BOOST_PP_WHILE_174_I(p, o, s)
# define BOOST_PP_WHILE_175(p, o, s) BOOST_PP_WHILE_175_I(p, o, s)
# define BOOST_PP_WHILE_176(p, o, s) BOOST_PP_WHILE_176_I(p, o, s)
# define BOOST_PP_WHILE_177(p, o, s) BOOST_PP_WHILE_177_I(p, o, s)
# define BOOST_PP_WHILE_178(p, o, s) BOOST_PP_WHILE_178_I(p, o, s)
# define BOOST_PP_WHILE_179(p, o, s) BOOST_PP_WHILE_179_I(p, o, s)
# define BOOST_PP_WHILE_180(p, o, s) BOOST_PP_WHILE_180_I(p, o, s)
# define BOOST_PP_WHILE_181(p, o, s) BOOST_PP_WHILE_181_I(p, o, s)
# define BOOST_PP_WHILE_182(p, o, s) BOOST_PP_WHILE_182_I(p, o, s)
# define BOOST_PP_WHILE_183(p, o, s) BOOST_PP_WHILE_183_I(p, o, s)
# define BOOST_PP_WHILE_184(p, o, s) BOOST_PP_WHILE_184_I(p, o, s)
# define BOOST_PP_WHILE_185(p, o, s) BOOST_PP_WHILE_185_I(p, o, s)
# define BOOST_PP_WHILE_186(p, o, s) BOOST_PP_WHILE_186_I(p, o, s)
# define BOOST_PP_WHILE_187(p, o, s) BOOST_PP_WHILE_187_I(p, o, s)
# define BOOST_PP_WHILE_188(p, o, s) BOOST_PP_WHILE_188_I(p, o, s)
# define BOOST_PP_WHILE_189(p, o, s) BOOST_PP_WHILE_189_I(p, o, s)
# define BOOST_PP_WHILE_190(p, o, s) BOOST_PP_WHILE_190_I(p, o, s)
# define BOOST_PP_WHILE_191(p, o, s) BOOST_PP_WHILE_191_I(p, o, s)
# define BOOST_PP_WHILE_192(p, o, s) BOOST_PP_WHILE_192_I(p, o, s)
# define BOOST_PP_WHILE_193(p, o, s) BOOST_PP_WHILE_193_I(p, o, s)
# define BOOST_PP_WHILE_194(p, o, s) BOOST_PP_WHILE_194_I(p, o, s)
# define BOOST_PP_WHILE_195(p, o, s) BOOST_PP_WHILE_195_I(p, o, s)
# define BOOST_PP_WHILE_196(p, o, s) BOOST_PP_WHILE_196_I(p, o, s)
# define BOOST_PP_WHILE_197(p, o, s) BOOST_PP_WHILE_197_I(p, o, s)
# define BOOST_PP_WHILE_198(p, o, s) BOOST_PP_WHILE_198_I(p, o, s)
# define BOOST_PP_WHILE_199(p, o, s) BOOST_PP_WHILE_199_I(p, o, s)
# define BOOST_PP_WHILE_200(p, o, s) BOOST_PP_WHILE_200_I(p, o, s)
# define BOOST_PP_WHILE_201(p, o, s) BOOST_PP_WHILE_201_I(p, o, s)
# define BOOST_PP_WHILE_202(p, o, s) BOOST_PP_WHILE_202_I(p, o, s)
# define BOOST_PP_WHILE_203(p, o, s) BOOST_PP_WHILE_203_I(p, o, s)
# define BOOST_PP_WHILE_204(p, o, s) BOOST_PP_WHILE_204_I(p, o, s)
# define BOOST_PP_WHILE_205(p, o, s) BOOST_PP_WHILE_205_I(p, o, s)
# define BOOST_PP_WHILE_206(p, o, s) BOOST_PP_WHILE_206_I(p, o, s)
# define BOOST_PP_WHILE_207(p, o, s) BOOST_PP_WHILE_207_I(p, o, s)
# define BOOST_PP_WHILE_208(p, o, s) BOOST_PP_WHILE_208_I(p, o, s)
# define BOOST_PP_WHILE_209(p, o, s) BOOST_PP_WHILE_209_I(p, o, s)
# define BOOST_PP_WHILE_210(p, o, s) BOOST_PP_WHILE_210_I(p, o, s)
# define BOOST_PP_WHILE_211(p, o, s) BOOST_PP_WHILE_211_I(p, o, s)
# define BOOST_PP_WHILE_212(p, o, s) BOOST_PP_WHILE_212_I(p, o, s)
# define BOOST_PP_WHILE_213(p, o, s) BOOST_PP_WHILE_213_I(p, o, s)
# define BOOST_PP_WHILE_214(p, o, s) BOOST_PP_WHILE_214_I(p, o, s)
# define BOOST_PP_WHILE_215(p, o, s) BOOST_PP_WHILE_215_I(p, o, s)
# define BOOST_PP_WHILE_216(p, o, s) BOOST_PP_WHILE_216_I(p, o, s)
# define BOOST_PP_WHILE_217(p, o, s) BOOST_PP_WHILE_217_I(p, o, s)
# define BOOST_PP_WHILE_218(p, o, s) BOOST_PP_WHILE_218_I(p, o, s)
# define BOOST_PP_WHILE_219(p, o, s) BOOST_PP_WHILE_219_I(p, o, s)
# define BOOST_PP_WHILE_220(p, o, s) BOOST_PP_WHILE_220_I(p, o, s)
# define BOOST_PP_WHILE_221(p, o, s) BOOST_PP_WHILE_221_I(p, o, s)
# define BOOST_PP_WHILE_222(p, o, s) BOOST_PP_WHILE_222_I(p, o, s)
# define BOOST_PP_WHILE_223(p, o, s) BOOST_PP_WHILE_223_I(p, o, s)
# define BOOST_PP_WHILE_224(p, o, s) BOOST_PP_WHILE_224_I(p, o, s)
# define BOOST_PP_WHILE_225(p, o, s) BOOST_PP_WHILE_225_I(p, o, s)
# define BOOST_PP_WHILE_226(p, o, s) BOOST_PP_WHILE_226_I(p, o, s)
# define BOOST_PP_WHILE_227(p, o, s) BOOST_PP_WHILE_227_I(p, o, s)
# define BOOST_PP_WHILE_228(p, o, s) BOOST_PP_WHILE_228_I(p, o, s)
# define BOOST_PP_WHILE_229(p, o, s) BOOST_PP_WHILE_229_I(p, o, s)
# define BOOST_PP_WHILE_230(p, o, s) BOOST_PP_WHILE_230_I(p, o, s)
# define BOOST_PP_WHILE_231(p, o, s) BOOST_PP_WHILE_231_I(p, o, s)
# define BOOST_PP_WHILE_232(p, o, s) BOOST_PP_WHILE_232_I(p, o, s)
# define BOOST_PP_WHILE_233(p, o, s) BOOST_PP_WHILE_233_I(p, o, s)
# define BOOST_PP_WHILE_234(p, o, s) BOOST_PP_WHILE_234_I(p, o, s)
# define BOOST_PP_WHILE_235(p, o, s) BOOST_PP_WHILE_235_I(p, o, s)
# define BOOST_PP_WHILE_236(p, o, s) BOOST_PP_WHILE_236_I(p, o, s)
# define BOOST_PP_WHILE_237(p, o, s) BOOST_PP_WHILE_237_I(p, o, s)
# define BOOST_PP_WHILE_238(p, o, s) BOOST_PP_WHILE_238_I(p, o, s)
# define BOOST_PP_WHILE_239(p, o, s) BOOST_PP_WHILE_239_I(p, o, s)
# define BOOST_PP_WHILE_240(p, o, s) BOOST_PP_WHILE_240_I(p, o, s)
# define BOOST_PP_WHILE_241(p, o, s) BOOST_PP_WHILE_241_I(p, o, s)
# define BOOST_PP_WHILE_242(p, o, s) BOOST_PP_WHILE_242_I(p, o, s)
# define BOOST_PP_WHILE_243(p, o, s) BOOST_PP_WHILE_243_I(p, o, s)
# define BOOST_PP_WHILE_244(p, o, s) BOOST_PP_WHILE_244_I(p, o, s)
# define BOOST_PP_WHILE_245(p, o, s) BOOST_PP_WHILE_245_I(p, o, s)
# define BOOST_PP_WHILE_246(p, o, s) BOOST_PP_WHILE_246_I(p, o, s)
# define BOOST_PP_WHILE_247(p, o, s) BOOST_PP_WHILE_247_I(p, o, s)
# define BOOST_PP_WHILE_248(p, o, s) BOOST_PP_WHILE_248_I(p, o, s)
# define BOOST_PP_WHILE_249(p, o, s) BOOST_PP_WHILE_249_I(p, o, s)
# define BOOST_PP_WHILE_250(p, o, s) BOOST_PP_WHILE_250_I(p, o, s)
# define BOOST_PP_WHILE_251(p, o, s) BOOST_PP_WHILE_251_I(p, o, s)
# define BOOST_PP_WHILE_252(p, o, s) BOOST_PP_WHILE_252_I(p, o, s)
# define BOOST_PP_WHILE_253(p, o, s) BOOST_PP_WHILE_253_I(p, o, s)
# define BOOST_PP_WHILE_254(p, o, s) BOOST_PP_WHILE_254_I(p, o, s)
# define BOOST_PP_WHILE_255(p, o, s) BOOST_PP_WHILE_255_I(p, o, s)
# define BOOST_PP_WHILE_256(p, o, s) BOOST_PP_WHILE_256_I(p, o, s)
#
# define BOOST_PP_WHILE_0_I(p, o, s) BOOST_PP_IF(p(1, s), BOOST_PP_WHILE_1, s BOOST_PP_TUPLE_EAT_3)(p, o, o(1, s))
# define BOOST_PP_WHILE_1_I(p, o, s) BOOST_PP_IF(p(2, s), BOOST_PP_WHILE_2, s BOOST_PP_TUPLE_EAT_3)(p, o, o(2, s))
# define BOOST_PP_WHILE_2_I(p, o, s) BOOST_PP_IF(p(3, s), BOOST_PP_WHILE_3, s BOOST_PP_TUPLE_EAT_3)(p, o, o(3, s))
# define BOOST_PP_WHILE_3_I(p, o, s) BOOST_PP_IF(p(4, s), BOOST_PP_WHILE_4, s BOOST_PP_TUPLE_EAT_3)(p, o, o(4, s))
# define BOOST_PP_WHILE_4_I(p, o, s) BOOST_PP_IF(p(5, s), BOOST_PP_WHILE_5, s BOOST_PP_TUPLE_EAT_3)(p, o, o(5, s))
# define BOOST_PP_WHILE_5_I(p, o, s) BOOST_PP_IF(p(6, s), BOOST_PP_WHILE_6, s BOOST_PP_TUPLE_EAT_3)(p, o, o(6, s))
# define BOOST_PP_WHILE_6_I(p, o, s) BOOST_PP_IF(p(7, s), BOOST_PP_WHILE_7, s BOOST_PP_TUPLE_EAT_3)(p, o, o(7, s))
# define BOOST_PP_WHILE_7_I(p, o, s) BOOST_PP_IF(p(8, s), BOOST_PP_WHILE_8, s BOOST_PP_TUPLE_EAT_3)(p, o, o(8, s))
# define BOOST_PP_WHILE_8_I(p, o, s) BOOST_PP_IF(p(9, s), BOOST_PP_WHILE_9, s BOOST_PP_TUPLE_EAT_3)(p, o, o(9, s))
# define BOOST_PP_WHILE_9_I(p, o, s) BOOST_PP_IF(p(10, s), BOOST_PP_WHILE_10, s BOOST_PP_TUPLE_EAT_3)(p, o, o(10, s))
# define BOOST_PP_WHILE_10_I(p, o, s) BOOST_PP_IF(p(11, s), BOOST_PP_WHILE_11, s BOOST_PP_TUPLE_EAT_3)(p, o, o(11, s))
# define BOOST_PP_WHILE_11_I(p, o, s) BOOST_PP_IF(p(12, s), BOOST_PP_WHILE_12, s BOOST_PP_TUPLE_EAT_3)(p, o, o(12, s))
# define BOOST_PP_WHILE_12_I(p, o, s) BOOST_PP_IF(p(13, s), BOOST_PP_WHILE_13, s BOOST_PP_TUPLE_EAT_3)(p, o, o(13, s))
# define BOOST_PP_WHILE_13_I(p, o, s) BOOST_PP_IF(p(14, s), BOOST_PP_WHILE_14, s BOOST_PP_TUPLE_EAT_3)(p, o, o(14, s))
# define BOOST_PP_WHILE_14_I(p, o, s) BOOST_PP_IF(p(15, s), BOOST_PP_WHILE_15, s BOOST_PP_TUPLE_EAT_3)(p, o, o(15, s))
# define BOOST_PP_WHILE_15_I(p, o, s) BOOST_PP_IF(p(16, s), BOOST_PP_WHILE_16, s BOOST_PP_TUPLE_EAT_3)(p, o, o(16, s))
# define BOOST_PP_WHILE_16_I(p, o, s) BOOST_PP_IF(p(17, s), BOOST_PP_WHILE_17, s BOOST_PP_TUPLE_EAT_3)(p, o, o(17, s))
# define BOOST_PP_WHILE_17_I(p, o, s) BOOST_PP_IF(p(18, s), BOOST_PP_WHILE_18, s BOOST_PP_TUPLE_EAT_3)(p, o, o(18, s))
# define BOOST_PP_WHILE_18_I(p, o, s) BOOST_PP_IF(p(19, s), BOOST_PP_WHILE_19, s BOOST_PP_TUPLE_EAT_3)(p, o, o(19, s))
# define BOOST_PP_WHILE_19_I(p, o, s) BOOST_PP_IF(p(20, s), BOOST_PP_WHILE_20, s BOOST_PP_TUPLE_EAT_3)(p, o, o(20, s))
# define BOOST_PP_WHILE_20_I(p, o, s) BOOST_PP_IF(p(21, s), BOOST_PP_WHILE_21, s BOOST_PP_TUPLE_EAT_3)(p, o, o(21, s))
# define BOOST_PP_WHILE_21_I(p, o, s) BOOST_PP_IF(p(22, s), BOOST_PP_WHILE_22, s BOOST_PP_TUPLE_EAT_3)(p, o, o(22, s))
# define BOOST_PP_WHILE_22_I(p, o, s) BOOST_PP_IF(p(23, s), BOOST_PP_WHILE_23, s BOOST_PP_TUPLE_EAT_3)(p, o, o(23, s))
# define BOOST_PP_WHILE_23_I(p, o, s) BOOST_PP_IF(p(24, s), BOOST_PP_WHILE_24, s BOOST_PP_TUPLE_EAT_3)(p, o, o(24, s))
# define BOOST_PP_WHILE_24_I(p, o, s) BOOST_PP_IF(p(25, s), BOOST_PP_WHILE_25, s BOOST_PP_TUPLE_EAT_3)(p, o, o(25, s))
# define BOOST_PP_WHILE_25_I(p, o, s) BOOST_PP_IF(p(26, s), BOOST_PP_WHILE_26, s BOOST_PP_TUPLE_EAT_3)(p, o, o(26, s))
# define BOOST_PP_WHILE_26_I(p, o, s) BOOST_PP_IF(p(27, s), BOOST_PP_WHILE_27, s BOOST_PP_TUPLE_EAT_3)(p, o, o(27, s))
# define BOOST_PP_WHILE_27_I(p, o, s) BOOST_PP_IF(p(28, s), BOOST_PP_WHILE_28, s BOOST_PP_TUPLE_EAT_3)(p, o, o(28, s))
# define BOOST_PP_WHILE_28_I(p, o, s) BOOST_PP_IF(p(29, s), BOOST_PP_WHILE_29, s BOOST_PP_TUPLE_EAT_3)(p, o, o(29, s))
# define BOOST_PP_WHILE_29_I(p, o, s) BOOST_PP_IF(p(30, s), BOOST_PP_WHILE_30, s BOOST_PP_TUPLE_EAT_3)(p, o, o(30, s))
# define BOOST_PP_WHILE_30_I(p, o, s) BOOST_PP_IF(p(31, s), BOOST_PP_WHILE_31, s BOOST_PP_TUPLE_EAT_3)(p, o, o(31, s))
# define BOOST_PP_WHILE_31_I(p, o, s) BOOST_PP_IF(p(32, s), BOOST_PP_WHILE_32, s BOOST_PP_TUPLE_EAT_3)(p, o, o(32, s))
# define BOOST_PP_WHILE_32_I(p, o, s) BOOST_PP_IF(p(33, s), BOOST_PP_WHILE_33, s BOOST_PP_TUPLE_EAT_3)(p, o, o(33, s))
# define BOOST_PP_WHILE_33_I(p, o, s) BOOST_PP_IF(p(34, s), BOOST_PP_WHILE_34, s BOOST_PP_TUPLE_EAT_3)(p, o, o(34, s))
# define BOOST_PP_WHILE_34_I(p, o, s) BOOST_PP_IF(p(35, s), BOOST_PP_WHILE_35, s BOOST_PP_TUPLE_EAT_3)(p, o, o(35, s))
# define BOOST_PP_WHILE_35_I(p, o, s) BOOST_PP_IF(p(36, s), BOOST_PP_WHILE_36, s BOOST_PP_TUPLE_EAT_3)(p, o, o(36, s))
# define BOOST_PP_WHILE_36_I(p, o, s) BOOST_PP_IF(p(37, s), BOOST_PP_WHILE_37, s BOOST_PP_TUPLE_EAT_3)(p, o, o(37, s))
# define BOOST_PP_WHILE_37_I(p, o, s) BOOST_PP_IF(p(38, s), BOOST_PP_WHILE_38, s BOOST_PP_TUPLE_EAT_3)(p, o, o(38, s))
# define BOOST_PP_WHILE_38_I(p, o, s) BOOST_PP_IF(p(39, s), BOOST_PP_WHILE_39, s BOOST_PP_TUPLE_EAT_3)(p, o, o(39, s))
# define BOOST_PP_WHILE_39_I(p, o, s) BOOST_PP_IF(p(40, s), BOOST_PP_WHILE_40, s BOOST_PP_TUPLE_EAT_3)(p, o, o(40, s))
# define BOOST_PP_WHILE_40_I(p, o, s) BOOST_PP_IF(p(41, s), BOOST_PP_WHILE_41, s BOOST_PP_TUPLE_EAT_3)(p, o, o(41, s))
# define BOOST_PP_WHILE_41_I(p, o, s) BOOST_PP_IF(p(42, s), BOOST_PP_WHILE_42, s BOOST_PP_TUPLE_EAT_3)(p, o, o(42, s))
# define BOOST_PP_WHILE_42_I(p, o, s) BOOST_PP_IF(p(43, s), BOOST_PP_WHILE_43, s BOOST_PP_TUPLE_EAT_3)(p, o, o(43, s))
# define BOOST_PP_WHILE_43_I(p, o, s) BOOST_PP_IF(p(44, s), BOOST_PP_WHILE_44, s BOOST_PP_TUPLE_EAT_3)(p, o, o(44, s))
# define BOOST_PP_WHILE_44_I(p, o, s) BOOST_PP_IF(p(45, s), BOOST_PP_WHILE_45, s BOOST_PP_TUPLE_EAT_3)(p, o, o(45, s))
# define BOOST_PP_WHILE_45_I(p, o, s) BOOST_PP_IF(p(46, s), BOOST_PP_WHILE_46, s BOOST_PP_TUPLE_EAT_3)(p, o, o(46, s))
# define BOOST_PP_WHILE_46_I(p, o, s) BOOST_PP_IF(p(47, s), BOOST_PP_WHILE_47, s BOOST_PP_TUPLE_EAT_3)(p, o, o(47, s))
# define BOOST_PP_WHILE_47_I(p, o, s) BOOST_PP_IF(p(48, s), BOOST_PP_WHILE_48, s BOOST_PP_TUPLE_EAT_3)(p, o, o(48, s))
# define BOOST_PP_WHILE_48_I(p, o, s) BOOST_PP_IF(p(49, s), BOOST_PP_WHILE_49, s BOOST_PP_TUPLE_EAT_3)(p, o, o(49, s))
# define BOOST_PP_WHILE_49_I(p, o, s) BOOST_PP_IF(p(50, s), BOOST_PP_WHILE_50, s BOOST_PP_TUPLE_EAT_3)(p, o, o(50, s))
# define BOOST_PP_WHILE_50_I(p, o, s) BOOST_PP_IF(p(51, s), BOOST_PP_WHILE_51, s BOOST_PP_TUPLE_EAT_3)(p, o, o(51, s))
# define BOOST_PP_WHILE_51_I(p, o, s) BOOST_PP_IF(p(52, s), BOOST_PP_WHILE_52, s BOOST_PP_TUPLE_EAT_3)(p, o, o(52, s))
# define BOOST_PP_WHILE_52_I(p, o, s) BOOST_PP_IF(p(53, s), BOOST_PP_WHILE_53, s BOOST_PP_TUPLE_EAT_3)(p, o, o(53, s))
# define BOOST_PP_WHILE_53_I(p, o, s) BOOST_PP_IF(p(54, s), BOOST_PP_WHILE_54, s BOOST_PP_TUPLE_EAT_3)(p, o, o(54, s))
# define BOOST_PP_WHILE_54_I(p, o, s) BOOST_PP_IF(p(55, s), BOOST_PP_WHILE_55, s BOOST_PP_TUPLE_EAT_3)(p, o, o(55, s))
# define BOOST_PP_WHILE_55_I(p, o, s) BOOST_PP_IF(p(56, s), BOOST_PP_WHILE_56, s BOOST_PP_TUPLE_EAT_3)(p, o, o(56, s))
# define BOOST_PP_WHILE_56_I(p, o, s) BOOST_PP_IF(p(57, s), BOOST_PP_WHILE_57, s BOOST_PP_TUPLE_EAT_3)(p, o, o(57, s))
# define BOOST_PP_WHILE_57_I(p, o, s) BOOST_PP_IF(p(58, s), BOOST_PP_WHILE_58, s BOOST_PP_TUPLE_EAT_3)(p, o, o(58, s))
# define BOOST_PP_WHILE_58_I(p, o, s) BOOST_PP_IF(p(59, s), BOOST_PP_WHILE_59, s BOOST_PP_TUPLE_EAT_3)(p, o, o(59, s))
# define BOOST_PP_WHILE_59_I(p, o, s) BOOST_PP_IF(p(60, s), BOOST_PP_WHILE_60, s BOOST_PP_TUPLE_EAT_3)(p, o, o(60, s))
# define BOOST_PP_WHILE_60_I(p, o, s) BOOST_PP_IF(p(61, s), BOOST_PP_WHILE_61, s BOOST_PP_TUPLE_EAT_3)(p, o, o(61, s))
# define BOOST_PP_WHILE_61_I(p, o, s) BOOST_PP_IF(p(62, s), BOOST_PP_WHILE_62, s BOOST_PP_TUPLE_EAT_3)(p, o, o(62, s))
# define BOOST_PP_WHILE_62_I(p, o, s) BOOST_PP_IF(p(63, s), BOOST_PP_WHILE_63, s BOOST_PP_TUPLE_EAT_3)(p, o, o(63, s))
# define BOOST_PP_WHILE_63_I(p, o, s) BOOST_PP_IF(p(64, s), BOOST_PP_WHILE_64, s BOOST_PP_TUPLE_EAT_3)(p, o, o(64, s))
# define BOOST_PP_WHILE_64_I(p, o, s) BOOST_PP_IF(p(65, s), BOOST_PP_WHILE_65, s BOOST_PP_TUPLE_EAT_3)(p, o, o(65, s))
# define BOOST_PP_WHILE_65_I(p, o, s) BOOST_PP_IF(p(66, s), BOOST_PP_WHILE_66, s BOOST_PP_TUPLE_EAT_3)(p, o, o(66, s))
# define BOOST_PP_WHILE_66_I(p, o, s) BOOST_PP_IF(p(67, s), BOOST_PP_WHILE_67, s BOOST_PP_TUPLE_EAT_3)(p, o, o(67, s))
# define BOOST_PP_WHILE_67_I(p, o, s) BOOST_PP_IF(p(68, s), BOOST_PP_WHILE_68, s BOOST_PP_TUPLE_EAT_3)(p, o, o(68, s))
# define BOOST_PP_WHILE_68_I(p, o, s) BOOST_PP_IF(p(69, s), BOOST_PP_WHILE_69, s BOOST_PP_TUPLE_EAT_3)(p, o, o(69, s))
# define BOOST_PP_WHILE_69_I(p, o, s) BOOST_PP_IF(p(70, s), BOOST_PP_WHILE_70, s BOOST_PP_TUPLE_EAT_3)(p, o, o(70, s))
# define BOOST_PP_WHILE_70_I(p, o, s) BOOST_PP_IF(p(71, s), BOOST_PP_WHILE_71, s BOOST_PP_TUPLE_EAT_3)(p, o, o(71, s))
# define BOOST_PP_WHILE_71_I(p, o, s) BOOST_PP_IF(p(72, s), BOOST_PP_WHILE_72, s BOOST_PP_TUPLE_EAT_3)(p, o, o(72, s))
# define BOOST_PP_WHILE_72_I(p, o, s) BOOST_PP_IF(p(73, s), BOOST_PP_WHILE_73, s BOOST_PP_TUPLE_EAT_3)(p, o, o(73, s))
# define BOOST_PP_WHILE_73_I(p, o, s) BOOST_PP_IF(p(74, s), BOOST_PP_WHILE_74, s BOOST_PP_TUPLE_EAT_3)(p, o, o(74, s))
# define BOOST_PP_WHILE_74_I(p, o, s) BOOST_PP_IF(p(75, s), BOOST_PP_WHILE_75, s BOOST_PP_TUPLE_EAT_3)(p, o, o(75, s))
# define BOOST_PP_WHILE_75_I(p, o, s) BOOST_PP_IF(p(76, s), BOOST_PP_WHILE_76, s BOOST_PP_TUPLE_EAT_3)(p, o, o(76, s))
# define BOOST_PP_WHILE_76_I(p, o, s) BOOST_PP_IF(p(77, s), BOOST_PP_WHILE_77, s BOOST_PP_TUPLE_EAT_3)(p, o, o(77, s))
# define BOOST_PP_WHILE_77_I(p, o, s) BOOST_PP_IF(p(78, s), BOOST_PP_WHILE_78, s BOOST_PP_TUPLE_EAT_3)(p, o, o(78, s))
# define BOOST_PP_WHILE_78_I(p, o, s) BOOST_PP_IF(p(79, s), BOOST_PP_WHILE_79, s BOOST_PP_TUPLE_EAT_3)(p, o, o(79, s))
# define BOOST_PP_WHILE_79_I(p, o, s) BOOST_PP_IF(p(80, s), BOOST_PP_WHILE_80, s BOOST_PP_TUPLE_EAT_3)(p, o, o(80, s))
# define BOOST_PP_WHILE_80_I(p, o, s) BOOST_PP_IF(p(81, s), BOOST_PP_WHILE_81, s BOOST_PP_TUPLE_EAT_3)(p, o, o(81, s))
# define BOOST_PP_WHILE_81_I(p, o, s) BOOST_PP_IF(p(82, s), BOOST_PP_WHILE_82, s BOOST_PP_TUPLE_EAT_3)(p, o, o(82, s))
# define BOOST_PP_WHILE_82_I(p, o, s) BOOST_PP_IF(p(83, s), BOOST_PP_WHILE_83, s BOOST_PP_TUPLE_EAT_3)(p, o, o(83, s))
# define BOOST_PP_WHILE_83_I(p, o, s) BOOST_PP_IF(p(84, s), BOOST_PP_WHILE_84, s BOOST_PP_TUPLE_EAT_3)(p, o, o(84, s))
# define BOOST_PP_WHILE_84_I(p, o, s) BOOST_PP_IF(p(85, s), BOOST_PP_WHILE_85, s BOOST_PP_TUPLE_EAT_3)(p, o, o(85, s))
# define BOOST_PP_WHILE_85_I(p, o, s) BOOST_PP_IF(p(86, s), BOOST_PP_WHILE_86, s BOOST_PP_TUPLE_EAT_3)(p, o, o(86, s))
# define BOOST_PP_WHILE_86_I(p, o, s) BOOST_PP_IF(p(87, s), BOOST_PP_WHILE_87, s BOOST_PP_TUPLE_EAT_3)(p, o, o(87, s))
# define BOOST_PP_WHILE_87_I(p, o, s) BOOST_PP_IF(p(88, s), BOOST_PP_WHILE_88, s BOOST_PP_TUPLE_EAT_3)(p, o, o(88, s))
# define BOOST_PP_WHILE_88_I(p, o, s) BOOST_PP_IF(p(89, s), BOOST_PP_WHILE_89, s BOOST_PP_TUPLE_EAT_3)(p, o, o(89, s))
# define BOOST_PP_WHILE_89_I(p, o, s) BOOST_PP_IF(p(90, s), BOOST_PP_WHILE_90, s BOOST_PP_TUPLE_EAT_3)(p, o, o(90, s))
# define BOOST_PP_WHILE_90_I(p, o, s) BOOST_PP_IF(p(91, s), BOOST_PP_WHILE_91, s BOOST_PP_TUPLE_EAT_3)(p, o, o(91, s))
# define BOOST_PP_WHILE_91_I(p, o, s) BOOST_PP_IF(p(92, s), BOOST_PP_WHILE_92, s BOOST_PP_TUPLE_EAT_3)(p, o, o(92, s))
# define BOOST_PP_WHILE_92_I(p, o, s) BOOST_PP_IF(p(93, s), BOOST_PP_WHILE_93, s BOOST_PP_TUPLE_EAT_3)(p, o, o(93, s))
# define BOOST_PP_WHILE_93_I(p, o, s) BOOST_PP_IF(p(94, s), BOOST_PP_WHILE_94, s BOOST_PP_TUPLE_EAT_3)(p, o, o(94, s))
# define BOOST_PP_WHILE_94_I(p, o, s) BOOST_PP_IF(p(95, s), BOOST_PP_WHILE_95, s BOOST_PP_TUPLE_EAT_3)(p, o, o(95, s))
# define BOOST_PP_WHILE_95_I(p, o, s) BOOST_PP_IF(p(96, s), BOOST_PP_WHILE_96, s BOOST_PP_TUPLE_EAT_3)(p, o, o(96, s))
# define BOOST_PP_WHILE_96_I(p, o, s) BOOST_PP_IF(p(97, s), BOOST_PP_WHILE_97, s BOOST_PP_TUPLE_EAT_3)(p, o, o(97, s))
# define BOOST_PP_WHILE_97_I(p, o, s) BOOST_PP_IF(p(98, s), BOOST_PP_WHILE_98, s BOOST_PP_TUPLE_EAT_3)(p, o, o(98, s))
# define BOOST_PP_WHILE_98_I(p, o, s) BOOST_PP_IF(p(99, s), BOOST_PP_WHILE_99, s BOOST_PP_TUPLE_EAT_3)(p, o, o(99, s))
# define BOOST_PP_WHILE_99_I(p, o, s) BOOST_PP_IF(p(100, s), BOOST_PP_WHILE_100, s BOOST_PP_TUPLE_EAT_3)(p, o, o(100, s))
# define BOOST_PP_WHILE_100_I(p, o, s) BOOST_PP_IF(p(101, s), BOOST_PP_WHILE_101, s BOOST_PP_TUPLE_EAT_3)(p, o, o(101, s))
# define BOOST_PP_WHILE_101_I(p, o, s) BOOST_PP_IF(p(102, s), BOOST_PP_WHILE_102, s BOOST_PP_TUPLE_EAT_3)(p, o, o(102, s))
# define BOOST_PP_WHILE_102_I(p, o, s) BOOST_PP_IF(p(103, s), BOOST_PP_WHILE_103, s BOOST_PP_TUPLE_EAT_3)(p, o, o(103, s))
# define BOOST_PP_WHILE_103_I(p, o, s) BOOST_PP_IF(p(104, s), BOOST_PP_WHILE_104, s BOOST_PP_TUPLE_EAT_3)(p, o, o(104, s))
# define BOOST_PP_WHILE_104_I(p, o, s) BOOST_PP_IF(p(105, s), BOOST_PP_WHILE_105, s BOOST_PP_TUPLE_EAT_3)(p, o, o(105, s))
# define BOOST_PP_WHILE_105_I(p, o, s) BOOST_PP_IF(p(106, s), BOOST_PP_WHILE_106, s BOOST_PP_TUPLE_EAT_3)(p, o, o(106, s))
# define BOOST_PP_WHILE_106_I(p, o, s) BOOST_PP_IF(p(107, s), BOOST_PP_WHILE_107, s BOOST_PP_TUPLE_EAT_3)(p, o, o(107, s))
# define BOOST_PP_WHILE_107_I(p, o, s) BOOST_PP_IF(p(108, s), BOOST_PP_WHILE_108, s BOOST_PP_TUPLE_EAT_3)(p, o, o(108, s))
# define BOOST_PP_WHILE_108_I(p, o, s) BOOST_PP_IF(p(109, s), BOOST_PP_WHILE_109, s BOOST_PP_TUPLE_EAT_3)(p, o, o(109, s))
# define BOOST_PP_WHILE_109_I(p, o, s) BOOST_PP_IF(p(110, s), BOOST_PP_WHILE_110, s BOOST_PP_TUPLE_EAT_3)(p, o, o(110, s))
# define BOOST_PP_WHILE_110_I(p, o, s) BOOST_PP_IF(p(111, s), BOOST_PP_WHILE_111, s BOOST_PP_TUPLE_EAT_3)(p, o, o(111, s))
# define BOOST_PP_WHILE_111_I(p, o, s) BOOST_PP_IF(p(112, s), BOOST_PP_WHILE_112, s BOOST_PP_TUPLE_EAT_3)(p, o, o(112, s))
# define BOOST_PP_WHILE_112_I(p, o, s) BOOST_PP_IF(p(113, s), BOOST_PP_WHILE_113, s BOOST_PP_TUPLE_EAT_3)(p, o, o(113, s))
# define BOOST_PP_WHILE_113_I(p, o, s) BOOST_PP_IF(p(114, s), BOOST_PP_WHILE_114, s BOOST_PP_TUPLE_EAT_3)(p, o, o(114, s))
# define BOOST_PP_WHILE_114_I(p, o, s) BOOST_PP_IF(p(115, s), BOOST_PP_WHILE_115, s BOOST_PP_TUPLE_EAT_3)(p, o, o(115, s))
# define BOOST_PP_WHILE_115_I(p, o, s) BOOST_PP_IF(p(116, s), BOOST_PP_WHILE_116, s BOOST_PP_TUPLE_EAT_3)(p, o, o(116, s))
# define BOOST_PP_WHILE_116_I(p, o, s) BOOST_PP_IF(p(117, s), BOOST_PP_WHILE_117, s BOOST_PP_TUPLE_EAT_3)(p, o, o(117, s))
# define BOOST_PP_WHILE_117_I(p, o, s) BOOST_PP_IF(p(118, s), BOOST_PP_WHILE_118, s BOOST_PP_TUPLE_EAT_3)(p, o, o(118, s))
# define BOOST_PP_WHILE_118_I(p, o, s) BOOST_PP_IF(p(119, s), BOOST_PP_WHILE_119, s BOOST_PP_TUPLE_EAT_3)(p, o, o(119, s))
# define BOOST_PP_WHILE_119_I(p, o, s) BOOST_PP_IF(p(120, s), BOOST_PP_WHILE_120, s BOOST_PP_TUPLE_EAT_3)(p, o, o(120, s))
# define BOOST_PP_WHILE_120_I(p, o, s) BOOST_PP_IF(p(121, s), BOOST_PP_WHILE_121, s BOOST_PP_TUPLE_EAT_3)(p, o, o(121, s))
# define BOOST_PP_WHILE_121_I(p, o, s) BOOST_PP_IF(p(122, s), BOOST_PP_WHILE_122, s BOOST_PP_TUPLE_EAT_3)(p, o, o(122, s))
# define BOOST_PP_WHILE_122_I(p, o, s) BOOST_PP_IF(p(123, s), BOOST_PP_WHILE_123, s BOOST_PP_TUPLE_EAT_3)(p, o, o(123, s))
# define BOOST_PP_WHILE_123_I(p, o, s) BOOST_PP_IF(p(124, s), BOOST_PP_WHILE_124, s BOOST_PP_TUPLE_EAT_3)(p, o, o(124, s))
# define BOOST_PP_WHILE_124_I(p, o, s) BOOST_PP_IF(p(125, s), BOOST_PP_WHILE_125, s BOOST_PP_TUPLE_EAT_3)(p, o, o(125, s))
# define BOOST_PP_WHILE_125_I(p, o, s) BOOST_PP_IF(p(126, s), BOOST_PP_WHILE_126, s BOOST_PP_TUPLE_EAT_3)(p, o, o(126, s))
# define BOOST_PP_WHILE_126_I(p, o, s) BOOST_PP_IF(p(127, s), BOOST_PP_WHILE_127, s BOOST_PP_TUPLE_EAT_3)(p, o, o(127, s))
# define BOOST_PP_WHILE_127_I(p, o, s) BOOST_PP_IF(p(128, s), BOOST_PP_WHILE_128, s BOOST_PP_TUPLE_EAT_3)(p, o, o(128, s))
# define BOOST_PP_WHILE_128_I(p, o, s) BOOST_PP_IF(p(129, s), BOOST_PP_WHILE_129, s BOOST_PP_TUPLE_EAT_3)(p, o, o(129, s))
# define BOOST_PP_WHILE_129_I(p, o, s) BOOST_PP_IF(p(130, s), BOOST_PP_WHILE_130, s BOOST_PP_TUPLE_EAT_3)(p, o, o(130, s))
# define BOOST_PP_WHILE_130_I(p, o, s) BOOST_PP_IF(p(131, s), BOOST_PP_WHILE_131, s BOOST_PP_TUPLE_EAT_3)(p, o, o(131, s))
# define BOOST_PP_WHILE_131_I(p, o, s) BOOST_PP_IF(p(132, s), BOOST_PP_WHILE_132, s BOOST_PP_TUPLE_EAT_3)(p, o, o(132, s))
# define BOOST_PP_WHILE_132_I(p, o, s) BOOST_PP_IF(p(133, s), BOOST_PP_WHILE_133, s BOOST_PP_TUPLE_EAT_3)(p, o, o(133, s))
# define BOOST_PP_WHILE_133_I(p, o, s) BOOST_PP_IF(p(134, s), BOOST_PP_WHILE_134, s BOOST_PP_TUPLE_EAT_3)(p, o, o(134, s))
# define BOOST_PP_WHILE_134_I(p, o, s) BOOST_PP_IF(p(135, s), BOOST_PP_WHILE_135, s BOOST_PP_TUPLE_EAT_3)(p, o, o(135, s))
# define BOOST_PP_WHILE_135_I(p, o, s) BOOST_PP_IF(p(136, s), BOOST_PP_WHILE_136, s BOOST_PP_TUPLE_EAT_3)(p, o, o(136, s))
# define BOOST_PP_WHILE_136_I(p, o, s) BOOST_PP_IF(p(137, s), BOOST_PP_WHILE_137, s BOOST_PP_TUPLE_EAT_3)(p, o, o(137, s))
# define BOOST_PP_WHILE_137_I(p, o, s) BOOST_PP_IF(p(138, s), BOOST_PP_WHILE_138, s BOOST_PP_TUPLE_EAT_3)(p, o, o(138, s))
# define BOOST_PP_WHILE_138_I(p, o, s) BOOST_PP_IF(p(139, s), BOOST_PP_WHILE_139, s BOOST_PP_TUPLE_EAT_3)(p, o, o(139, s))
# define BOOST_PP_WHILE_139_I(p, o, s) BOOST_PP_IF(p(140, s), BOOST_PP_WHILE_140, s BOOST_PP_TUPLE_EAT_3)(p, o, o(140, s))
# define BOOST_PP_WHILE_140_I(p, o, s) BOOST_PP_IF(p(141, s), BOOST_PP_WHILE_141, s BOOST_PP_TUPLE_EAT_3)(p, o, o(141, s))
# define BOOST_PP_WHILE_141_I(p, o, s) BOOST_PP_IF(p(142, s), BOOST_PP_WHILE_142, s BOOST_PP_TUPLE_EAT_3)(p, o, o(142, s))
# define BOOST_PP_WHILE_142_I(p, o, s) BOOST_PP_IF(p(143, s), BOOST_PP_WHILE_143, s BOOST_PP_TUPLE_EAT_3)(p, o, o(143, s))
# define BOOST_PP_WHILE_143_I(p, o, s) BOOST_PP_IF(p(144, s), BOOST_PP_WHILE_144, s BOOST_PP_TUPLE_EAT_3)(p, o, o(144, s))
# define BOOST_PP_WHILE_144_I(p, o, s) BOOST_PP_IF(p(145, s), BOOST_PP_WHILE_145, s BOOST_PP_TUPLE_EAT_3)(p, o, o(145, s))
# define BOOST_PP_WHILE_145_I(p, o, s) BOOST_PP_IF(p(146, s), BOOST_PP_WHILE_146, s BOOST_PP_TUPLE_EAT_3)(p, o, o(146, s))
# define BOOST_PP_WHILE_146_I(p, o, s) BOOST_PP_IF(p(147, s), BOOST_PP_WHILE_147, s BOOST_PP_TUPLE_EAT_3)(p, o, o(147, s))
# define BOOST_PP_WHILE_147_I(p, o, s) BOOST_PP_IF(p(148, s), BOOST_PP_WHILE_148, s BOOST_PP_TUPLE_EAT_3)(p, o, o(148, s))
# define BOOST_PP_WHILE_148_I(p, o, s) BOOST_PP_IF(p(149, s), BOOST_PP_WHILE_149, s BOOST_PP_TUPLE_EAT_3)(p, o, o(149, s))
# define BOOST_PP_WHILE_149_I(p, o, s) BOOST_PP_IF(p(150, s), BOOST_PP_WHILE_150, s BOOST_PP_TUPLE_EAT_3)(p, o, o(150, s))
# define BOOST_PP_WHILE_150_I(p, o, s) BOOST_PP_IF(p(151, s), BOOST_PP_WHILE_151, s BOOST_PP_TUPLE_EAT_3)(p, o, o(151, s))
# define BOOST_PP_WHILE_151_I(p, o, s) BOOST_PP_IF(p(152, s), BOOST_PP_WHILE_152, s BOOST_PP_TUPLE_EAT_3)(p, o, o(152, s))
# define BOOST_PP_WHILE_152_I(p, o, s) BOOST_PP_IF(p(153, s), BOOST_PP_WHILE_153, s BOOST_PP_TUPLE_EAT_3)(p, o, o(153, s))
# define BOOST_PP_WHILE_153_I(p, o, s) BOOST_PP_IF(p(154, s), BOOST_PP_WHILE_154, s BOOST_PP_TUPLE_EAT_3)(p, o, o(154, s))
# define BOOST_PP_WHILE_154_I(p, o, s) BOOST_PP_IF(p(155, s), BOOST_PP_WHILE_155, s BOOST_PP_TUPLE_EAT_3)(p, o, o(155, s))
# define BOOST_PP_WHILE_155_I(p, o, s) BOOST_PP_IF(p(156, s), BOOST_PP_WHILE_156, s BOOST_PP_TUPLE_EAT_3)(p, o, o(156, s))
# define BOOST_PP_WHILE_156_I(p, o, s) BOOST_PP_IF(p(157, s), BOOST_PP_WHILE_157, s BOOST_PP_TUPLE_EAT_3)(p, o, o(157, s))
# define BOOST_PP_WHILE_157_I(p, o, s) BOOST_PP_IF(p(158, s), BOOST_PP_WHILE_158, s BOOST_PP_TUPLE_EAT_3)(p, o, o(158, s))
# define BOOST_PP_WHILE_158_I(p, o, s) BOOST_PP_IF(p(159, s), BOOST_PP_WHILE_159, s BOOST_PP_TUPLE_EAT_3)(p, o, o(159, s))
# define BOOST_PP_WHILE_159_I(p, o, s) BOOST_PP_IF(p(160, s), BOOST_PP_WHILE_160, s BOOST_PP_TUPLE_EAT_3)(p, o, o(160, s))
# define BOOST_PP_WHILE_160_I(p, o, s) BOOST_PP_IF(p(161, s), BOOST_PP_WHILE_161, s BOOST_PP_TUPLE_EAT_3)(p, o, o(161, s))
# define BOOST_PP_WHILE_161_I(p, o, s) BOOST_PP_IF(p(162, s), BOOST_PP_WHILE_162, s BOOST_PP_TUPLE_EAT_3)(p, o, o(162, s))
# define BOOST_PP_WHILE_162_I(p, o, s) BOOST_PP_IF(p(163, s), BOOST_PP_WHILE_163, s BOOST_PP_TUPLE_EAT_3)(p, o, o(163, s))
# define BOOST_PP_WHILE_163_I(p, o, s) BOOST_PP_IF(p(164, s), BOOST_PP_WHILE_164, s BOOST_PP_TUPLE_EAT_3)(p, o, o(164, s))
# define BOOST_PP_WHILE_164_I(p, o, s) BOOST_PP_IF(p(165, s), BOOST_PP_WHILE_165, s BOOST_PP_TUPLE_EAT_3)(p, o, o(165, s))
# define BOOST_PP_WHILE_165_I(p, o, s) BOOST_PP_IF(p(166, s), BOOST_PP_WHILE_166, s BOOST_PP_TUPLE_EAT_3)(p, o, o(166, s))
# define BOOST_PP_WHILE_166_I(p, o, s) BOOST_PP_IF(p(167, s), BOOST_PP_WHILE_167, s BOOST_PP_TUPLE_EAT_3)(p, o, o(167, s))
# define BOOST_PP_WHILE_167_I(p, o, s) BOOST_PP_IF(p(168, s), BOOST_PP_WHILE_168, s BOOST_PP_TUPLE_EAT_3)(p, o, o(168, s))
# define BOOST_PP_WHILE_168_I(p, o, s) BOOST_PP_IF(p(169, s), BOOST_PP_WHILE_169, s BOOST_PP_TUPLE_EAT_3)(p, o, o(169, s))
# define BOOST_PP_WHILE_169_I(p, o, s) BOOST_PP_IF(p(170, s), BOOST_PP_WHILE_170, s BOOST_PP_TUPLE_EAT_3)(p, o, o(170, s))
# define BOOST_PP_WHILE_170_I(p, o, s) BOOST_PP_IF(p(171, s), BOOST_PP_WHILE_171, s BOOST_PP_TUPLE_EAT_3)(p, o, o(171, s))
# define BOOST_PP_WHILE_171_I(p, o, s) BOOST_PP_IF(p(172, s), BOOST_PP_WHILE_172, s BOOST_PP_TUPLE_EAT_3)(p, o, o(172, s))
# define BOOST_PP_WHILE_172_I(p, o, s) BOOST_PP_IF(p(173, s), BOOST_PP_WHILE_173, s BOOST_PP_TUPLE_EAT_3)(p, o, o(173, s))
# define BOOST_PP_WHILE_173_I(p, o, s) BOOST_PP_IF(p(174, s), BOOST_PP_WHILE_174, s BOOST_PP_TUPLE_EAT_3)(p, o, o(174, s))
# define BOOST_PP_WHILE_174_I(p, o, s) BOOST_PP_IF(p(175, s), BOOST_PP_WHILE_175, s BOOST_PP_TUPLE_EAT_3)(p, o, o(175, s))
# define BOOST_PP_WHILE_175_I(p, o, s) BOOST_PP_IF(p(176, s), BOOST_PP_WHILE_176, s BOOST_PP_TUPLE_EAT_3)(p, o, o(176, s))
# define BOOST_PP_WHILE_176_I(p, o, s) BOOST_PP_IF(p(177, s), BOOST_PP_WHILE_177, s BOOST_PP_TUPLE_EAT_3)(p, o, o(177, s))
# define BOOST_PP_WHILE_177_I(p, o, s) BOOST_PP_IF(p(178, s), BOOST_PP_WHILE_178, s BOOST_PP_TUPLE_EAT_3)(p, o, o(178, s))
# define BOOST_PP_WHILE_178_I(p, o, s) BOOST_PP_IF(p(179, s), BOOST_PP_WHILE_179, s BOOST_PP_TUPLE_EAT_3)(p, o, o(179, s))
# define BOOST_PP_WHILE_179_I(p, o, s) BOOST_PP_IF(p(180, s), BOOST_PP_WHILE_180, s BOOST_PP_TUPLE_EAT_3)(p, o, o(180, s))
# define BOOST_PP_WHILE_180_I(p, o, s) BOOST_PP_IF(p(181, s), BOOST_PP_WHILE_181, s BOOST_PP_TUPLE_EAT_3)(p, o, o(181, s))
# define BOOST_PP_WHILE_181_I(p, o, s) BOOST_PP_IF(p(182, s), BOOST_PP_WHILE_182, s BOOST_PP_TUPLE_EAT_3)(p, o, o(182, s))
# define BOOST_PP_WHILE_182_I(p, o, s) BOOST_PP_IF(p(183, s), BOOST_PP_WHILE_183, s BOOST_PP_TUPLE_EAT_3)(p, o, o(183, s))
# define BOOST_PP_WHILE_183_I(p, o, s) BOOST_PP_IF(p(184, s), BOOST_PP_WHILE_184, s BOOST_PP_TUPLE_EAT_3)(p, o, o(184, s))
# define BOOST_PP_WHILE_184_I(p, o, s) BOOST_PP_IF(p(185, s), BOOST_PP_WHILE_185, s BOOST_PP_TUPLE_EAT_3)(p, o, o(185, s))
# define BOOST_PP_WHILE_185_I(p, o, s) BOOST_PP_IF(p(186, s), BOOST_PP_WHILE_186, s BOOST_PP_TUPLE_EAT_3)(p, o, o(186, s))
# define BOOST_PP_WHILE_186_I(p, o, s) BOOST_PP_IF(p(187, s), BOOST_PP_WHILE_187, s BOOST_PP_TUPLE_EAT_3)(p, o, o(187, s))
# define BOOST_PP_WHILE_187_I(p, o, s) BOOST_PP_IF(p(188, s), BOOST_PP_WHILE_188, s BOOST_PP_TUPLE_EAT_3)(p, o, o(188, s))
# define BOOST_PP_WHILE_188_I(p, o, s) BOOST_PP_IF(p(189, s), BOOST_PP_WHILE_189, s BOOST_PP_TUPLE_EAT_3)(p, o, o(189, s))
# define BOOST_PP_WHILE_189_I(p, o, s) BOOST_PP_IF(p(190, s), BOOST_PP_WHILE_190, s BOOST_PP_TUPLE_EAT_3)(p, o, o(190, s))
# define BOOST_PP_WHILE_190_I(p, o, s) BOOST_PP_IF(p(191, s), BOOST_PP_WHILE_191, s BOOST_PP_TUPLE_EAT_3)(p, o, o(191, s))
# define BOOST_PP_WHILE_191_I(p, o, s) BOOST_PP_IF(p(192, s), BOOST_PP_WHILE_192, s BOOST_PP_TUPLE_EAT_3)(p, o, o(192, s))
# define BOOST_PP_WHILE_192_I(p, o, s) BOOST_PP_IF(p(193, s), BOOST_PP_WHILE_193, s BOOST_PP_TUPLE_EAT_3)(p, o, o(193, s))
# define BOOST_PP_WHILE_193_I(p, o, s) BOOST_PP_IF(p(194, s), BOOST_PP_WHILE_194, s BOOST_PP_TUPLE_EAT_3)(p, o, o(194, s))
# define BOOST_PP_WHILE_194_I(p, o, s) BOOST_PP_IF(p(195, s), BOOST_PP_WHILE_195, s BOOST_PP_TUPLE_EAT_3)(p, o, o(195, s))
# define BOOST_PP_WHILE_195_I(p, o, s) BOOST_PP_IF(p(196, s), BOOST_PP_WHILE_196, s BOOST_PP_TUPLE_EAT_3)(p, o, o(196, s))
# define BOOST_PP_WHILE_196_I(p, o, s) BOOST_PP_IF(p(197, s), BOOST_PP_WHILE_197, s BOOST_PP_TUPLE_EAT_3)(p, o, o(197, s))
# define BOOST_PP_WHILE_197_I(p, o, s) BOOST_PP_IF(p(198, s), BOOST_PP_WHILE_198, s BOOST_PP_TUPLE_EAT_3)(p, o, o(198, s))
# define BOOST_PP_WHILE_198_I(p, o, s) BOOST_PP_IF(p(199, s), BOOST_PP_WHILE_199, s BOOST_PP_TUPLE_EAT_3)(p, o, o(199, s))
# define BOOST_PP_WHILE_199_I(p, o, s) BOOST_PP_IF(p(200, s), BOOST_PP_WHILE_200, s BOOST_PP_TUPLE_EAT_3)(p, o, o(200, s))
# define BOOST_PP_WHILE_200_I(p, o, s) BOOST_PP_IF(p(201, s), BOOST_PP_WHILE_201, s BOOST_PP_TUPLE_EAT_3)(p, o, o(201, s))
# define BOOST_PP_WHILE_201_I(p, o, s) BOOST_PP_IF(p(202, s), BOOST_PP_WHILE_202, s BOOST_PP_TUPLE_EAT_3)(p, o, o(202, s))
# define BOOST_PP_WHILE_202_I(p, o, s) BOOST_PP_IF(p(203, s), BOOST_PP_WHILE_203, s BOOST_PP_TUPLE_EAT_3)(p, o, o(203, s))
# define BOOST_PP_WHILE_203_I(p, o, s) BOOST_PP_IF(p(204, s), BOOST_PP_WHILE_204, s BOOST_PP_TUPLE_EAT_3)(p, o, o(204, s))
# define BOOST_PP_WHILE_204_I(p, o, s) BOOST_PP_IF(p(205, s), BOOST_PP_WHILE_205, s BOOST_PP_TUPLE_EAT_3)(p, o, o(205, s))
# define BOOST_PP_WHILE_205_I(p, o, s) BOOST_PP_IF(p(206, s), BOOST_PP_WHILE_206, s BOOST_PP_TUPLE_EAT_3)(p, o, o(206, s))
# define BOOST_PP_WHILE_206_I(p, o, s) BOOST_PP_IF(p(207, s), BOOST_PP_WHILE_207, s BOOST_PP_TUPLE_EAT_3)(p, o, o(207, s))
# define BOOST_PP_WHILE_207_I(p, o, s) BOOST_PP_IF(p(208, s), BOOST_PP_WHILE_208, s BOOST_PP_TUPLE_EAT_3)(p, o, o(208, s))
# define BOOST_PP_WHILE_208_I(p, o, s) BOOST_PP_IF(p(209, s), BOOST_PP_WHILE_209, s BOOST_PP_TUPLE_EAT_3)(p, o, o(209, s))
# define BOOST_PP_WHILE_209_I(p, o, s) BOOST_PP_IF(p(210, s), BOOST_PP_WHILE_210, s BOOST_PP_TUPLE_EAT_3)(p, o, o(210, s))
# define BOOST_PP_WHILE_210_I(p, o, s) BOOST_PP_IF(p(211, s), BOOST_PP_WHILE_211, s BOOST_PP_TUPLE_EAT_3)(p, o, o(211, s))
# define BOOST_PP_WHILE_211_I(p, o, s) BOOST_PP_IF(p(212, s), BOOST_PP_WHILE_212, s BOOST_PP_TUPLE_EAT_3)(p, o, o(212, s))
# define BOOST_PP_WHILE_212_I(p, o, s) BOOST_PP_IF(p(213, s), BOOST_PP_WHILE_213, s BOOST_PP_TUPLE_EAT_3)(p, o, o(213, s))
# define BOOST_PP_WHILE_213_I(p, o, s) BOOST_PP_IF(p(214, s), BOOST_PP_WHILE_214, s BOOST_PP_TUPLE_EAT_3)(p, o, o(214, s))
# define BOOST_PP_WHILE_214_I(p, o, s) BOOST_PP_IF(p(215, s), BOOST_PP_WHILE_215, s BOOST_PP_TUPLE_EAT_3)(p, o, o(215, s))
# define BOOST_PP_WHILE_215_I(p, o, s) BOOST_PP_IF(p(216, s), BOOST_PP_WHILE_216, s BOOST_PP_TUPLE_EAT_3)(p, o, o(216, s))
# define BOOST_PP_WHILE_216_I(p, o, s) BOOST_PP_IF(p(217, s), BOOST_PP_WHILE_217, s BOOST_PP_TUPLE_EAT_3)(p, o, o(217, s))
# define BOOST_PP_WHILE_217_I(p, o, s) BOOST_PP_IF(p(218, s), BOOST_PP_WHILE_218, s BOOST_PP_TUPLE_EAT_3)(p, o, o(218, s))
# define BOOST_PP_WHILE_218_I(p, o, s) BOOST_PP_IF(p(219, s), BOOST_PP_WHILE_219, s BOOST_PP_TUPLE_EAT_3)(p, o, o(219, s))
# define BOOST_PP_WHILE_219_I(p, o, s) BOOST_PP_IF(p(220, s), BOOST_PP_WHILE_220, s BOOST_PP_TUPLE_EAT_3)(p, o, o(220, s))
# define BOOST_PP_WHILE_220_I(p, o, s) BOOST_PP_IF(p(221, s), BOOST_PP_WHILE_221, s BOOST_PP_TUPLE_EAT_3)(p, o, o(221, s))
# define BOOST_PP_WHILE_221_I(p, o, s) BOOST_PP_IF(p(222, s), BOOST_PP_WHILE_222, s BOOST_PP_TUPLE_EAT_3)(p, o, o(222, s))
# define BOOST_PP_WHILE_222_I(p, o, s) BOOST_PP_IF(p(223, s), BOOST_PP_WHILE_223, s BOOST_PP_TUPLE_EAT_3)(p, o, o(223, s))
# define BOOST_PP_WHILE_223_I(p, o, s) BOOST_PP_IF(p(224, s), BOOST_PP_WHILE_224, s BOOST_PP_TUPLE_EAT_3)(p, o, o(224, s))
# define BOOST_PP_WHILE_224_I(p, o, s) BOOST_PP_IF(p(225, s), BOOST_PP_WHILE_225, s BOOST_PP_TUPLE_EAT_3)(p, o, o(225, s))
# define BOOST_PP_WHILE_225_I(p, o, s) BOOST_PP_IF(p(226, s), BOOST_PP_WHILE_226, s BOOST_PP_TUPLE_EAT_3)(p, o, o(226, s))
# define BOOST_PP_WHILE_226_I(p, o, s) BOOST_PP_IF(p(227, s), BOOST_PP_WHILE_227, s BOOST_PP_TUPLE_EAT_3)(p, o, o(227, s))
# define BOOST_PP_WHILE_227_I(p, o, s) BOOST_PP_IF(p(228, s), BOOST_PP_WHILE_228, s BOOST_PP_TUPLE_EAT_3)(p, o, o(228, s))
# define BOOST_PP_WHILE_228_I(p, o, s) BOOST_PP_IF(p(229, s), BOOST_PP_WHILE_229, s BOOST_PP_TUPLE_EAT_3)(p, o, o(229, s))
# define BOOST_PP_WHILE_229_I(p, o, s) BOOST_PP_IF(p(230, s), BOOST_PP_WHILE_230, s BOOST_PP_TUPLE_EAT_3)(p, o, o(230, s))
# define BOOST_PP_WHILE_230_I(p, o, s) BOOST_PP_IF(p(231, s), BOOST_PP_WHILE_231, s BOOST_PP_TUPLE_EAT_3)(p, o, o(231, s))
# define BOOST_PP_WHILE_231_I(p, o, s) BOOST_PP_IF(p(232, s), BOOST_PP_WHILE_232, s BOOST_PP_TUPLE_EAT_3)(p, o, o(232, s))
# define BOOST_PP_WHILE_232_I(p, o, s) BOOST_PP_IF(p(233, s), BOOST_PP_WHILE_233, s BOOST_PP_TUPLE_EAT_3)(p, o, o(233, s))
# define BOOST_PP_WHILE_233_I(p, o, s) BOOST_PP_IF(p(234, s), BOOST_PP_WHILE_234, s BOOST_PP_TUPLE_EAT_3)(p, o, o(234, s))
# define BOOST_PP_WHILE_234_I(p, o, s) BOOST_PP_IF(p(235, s), BOOST_PP_WHILE_235, s BOOST_PP_TUPLE_EAT_3)(p, o, o(235, s))
# define BOOST_PP_WHILE_235_I(p, o, s) BOOST_PP_IF(p(236, s), BOOST_PP_WHILE_236, s BOOST_PP_TUPLE_EAT_3)(p, o, o(236, s))
# define BOOST_PP_WHILE_236_I(p, o, s) BOOST_PP_IF(p(237, s), BOOST_PP_WHILE_237, s BOOST_PP_TUPLE_EAT_3)(p, o, o(237, s))
# define BOOST_PP_WHILE_237_I(p, o, s) BOOST_PP_IF(p(238, s), BOOST_PP_WHILE_238, s BOOST_PP_TUPLE_EAT_3)(p, o, o(238, s))
# define BOOST_PP_WHILE_238_I(p, o, s) BOOST_PP_IF(p(239, s), BOOST_PP_WHILE_239, s BOOST_PP_TUPLE_EAT_3)(p, o, o(239, s))
# define BOOST_PP_WHILE_239_I(p, o, s) BOOST_PP_IF(p(240, s), BOOST_PP_WHILE_240, s BOOST_PP_TUPLE_EAT_3)(p, o, o(240, s))
# define BOOST_PP_WHILE_240_I(p, o, s) BOOST_PP_IF(p(241, s), BOOST_PP_WHILE_241, s BOOST_PP_TUPLE_EAT_3)(p, o, o(241, s))
# define BOOST_PP_WHILE_241_I(p, o, s) BOOST_PP_IF(p(242, s), BOOST_PP_WHILE_242, s BOOST_PP_TUPLE_EAT_3)(p, o, o(242, s))
# define BOOST_PP_WHILE_242_I(p, o, s) BOOST_PP_IF(p(243, s), BOOST_PP_WHILE_243, s BOOST_PP_TUPLE_EAT_3)(p, o, o(243, s))
# define BOOST_PP_WHILE_243_I(p, o, s) BOOST_PP_IF(p(244, s), BOOST_PP_WHILE_244, s BOOST_PP_TUPLE_EAT_3)(p, o, o(244, s))
# define BOOST_PP_WHILE_244_I(p, o, s) BOOST_PP_IF(p(245, s), BOOST_PP_WHILE_245, s BOOST_PP_TUPLE_EAT_3)(p, o, o(245, s))
# define BOOST_PP_WHILE_245_I(p, o, s) BOOST_PP_IF(p(246, s), BOOST_PP_WHILE_246, s BOOST_PP_TUPLE_EAT_3)(p, o, o(246, s))
# define BOOST_PP_WHILE_246_I(p, o, s) BOOST_PP_IF(p(247, s), BOOST_PP_WHILE_247, s BOOST_PP_TUPLE_EAT_3)(p, o, o(247, s))
# define BOOST_PP_WHILE_247_I(p, o, s) BOOST_PP_IF(p(248, s), BOOST_PP_WHILE_248, s BOOST_PP_TUPLE_EAT_3)(p, o, o(248, s))
# define BOOST_PP_WHILE_248_I(p, o, s) BOOST_PP_IF(p(249, s), BOOST_PP_WHILE_249, s BOOST_PP_TUPLE_EAT_3)(p, o, o(249, s))
# define BOOST_PP_WHILE_249_I(p, o, s) BOOST_PP_IF(p(250, s), BOOST_PP_WHILE_250, s BOOST_PP_TUPLE_EAT_3)(p, o, o(250, s))
# define BOOST_PP_WHILE_250_I(p, o, s) BOOST_PP_IF(p(251, s), BOOST_PP_WHILE_251, s BOOST_PP_TUPLE_EAT_3)(p, o, o(251, s))
# define BOOST_PP_WHILE_251_I(p, o, s) BOOST_PP_IF(p(252, s), BOOST_PP_WHILE_252, s BOOST_PP_TUPLE_EAT_3)(p, o, o(252, s))
# define BOOST_PP_WHILE_252_I(p, o, s) BOOST_PP_IF(p(253, s), BOOST_PP_WHILE_253, s BOOST_PP_TUPLE_EAT_3)(p, o, o(253, s))
# define BOOST_PP_WHILE_253_I(p, o, s) BOOST_PP_IF(p(254, s), BOOST_PP_WHILE_254, s BOOST_PP_TUPLE_EAT_3)(p, o, o(254, s))
# define BOOST_PP_WHILE_254_I(p, o, s) BOOST_PP_IF(p(255, s), BOOST_PP_WHILE_255, s BOOST_PP_TUPLE_EAT_3)(p, o, o(255, s))
# define BOOST_PP_WHILE_255_I(p, o, s) BOOST_PP_IF(p(256, s), BOOST_PP_WHILE_256, s BOOST_PP_TUPLE_EAT_3)(p, o, o(256, s))
# define BOOST_PP_WHILE_256_I(p, o, s) BOOST_PP_IF(p(257, s), BOOST_PP_WHILE_257, s BOOST_PP_TUPLE_EAT_3)(p, o, o(257, s))
#
# endif
